# ===================================================================
# Standard Spring Boot properties.
# Full reference is available at:
# http://docs.spring.io/spring-boot/docs/current/reference/html/common-application-properties.html
# ===================================================================
seata:
  enabled: false
spring:
  application:
    name: doiov-ivsmp-websrv
  profiles:
    active: dev
  main:
    allow-circular-references: true # 允许循环依赖，因为项目是三层架构，无法避免这个情况。
    allow-bean-definition-overriding: true # 允许 Bean 覆盖，例如说 Dubbo 或者 Feign 等会存在重复定义的服务（放在nacos上不生效）
  cloud:
    nacos:
      discovery:
        register-enabled: true # false/不注册到nacos
        server-addr: 192.168.6.228:9000 # 注册中心配置
        namespace: 09d772f2-b17a-421e-a1ab-bb45a9788814
        group: DOIOV-IVSMP-GROUP
        username: 'nacos'
        password: 'Ctsi@2401.'
      config:
        server-addr: 192.168.6.228:9000 # 配置中心配置
        namespace: 09d772f2-b17a-421e-a1ab-bb45a9788814
        username: 'nacos'
        password: 'Ctsi@2401.'
        group: DOIOV-IVSMP-GROUP
        file-extension: yml
        shared-configs:
          - data-id: doiov-ivsmp-jdbc.yml
            group: DOIOV-IVSMP-GROUP
            #refresh: false
          - data-id: doiov-global-redis.yml
            group: DOIOV-GLOBAL-GROUP
#            #refresh: false
#          - data-id: doiov-global-cloud.yml
#            group: DOIOV-GLOBAL-GROUP
##            #refresh: false
#          - data-id: doiov-global-fastdfs.yml
#            group: DOIOV-GLOBAL-GROUP
            #refresh: false
          - data-id: doiov-global-common.yml
            group: DOIOV-GLOBAL-GROUP